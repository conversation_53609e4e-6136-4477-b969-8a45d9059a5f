# Managing Kubernetes with Rancher and OpenNebula CAPI Integration

## Introduction

This comprehensive tutorial demonstrates how to deploy and manage Kubernetes clusters using <PERSON><PERSON>'s web interface integrated with OpenNebula's Cluster API (CAPI) provider. **This tutorial has been thoroughly tested and debugged** to ensure all steps work correctly, including solutions for common issues encountered during deployment.

### What You'll Learn

- Deploy OpenNebula's CAPI appliance with Rancher
- Create and manage RKE2 Kubernetes clusters through Rancher's UI
- Deploy applications with persistent storage using Longhorn
- Scale clusters by adding worker nodes
- Upgrade Kubernetes clusters seamlessly
- Troubleshoot common deployment issues

### Architecture Overview

The solution consists of three main components:

1. **OpenNebula Infrastructure**: Provides the underlying virtualization platform
2. **CAPI Appliance**: A pre-configured VM running K3s with <PERSON><PERSON>, <PERSON>s extension, and CAPONE provider
3. **Workload Clusters**: RKE2 Kubernetes clusters managed through the CAPI provider

### Component Versions (Current Release)

- **K3s**: v1.31.7+k3s1
- **Rancher**: 2.11.1
- **Turtles**: 0.19.0
- **CAPONE**: v0.1.5
- **Cert-manager**: v1.17.2

## Prerequisites

Before starting, ensure you have:

### Infrastructure Requirements
- OpenNebula Front-end (version ≥6.10) with sufficient resources:
  - 4+ vCPUs available for the CAPI appliance
  - 16+ GiB RAM available for the CAPI appliance
  - Additional resources for workload cluster nodes (minimum 2 vCPUs, 4GB RAM per node)
- OneGate service enabled (default with miniONE installations)
- **Proper libvirt/qemu permissions configured** (critical for VM deployment)

### Network Requirements
- Public virtual network (automatically created by miniONE as `vnet`)
- Private virtual network (we'll create this in Step 1)

### Access Requirements
- Administrative access to OpenNebula Front-end
- Web browser for accessing Rancher UI
- Internet connectivity for the CAPI appliance

## CRITICAL: Fix libvirt/qemu Permissions First

**⚠️ IMPORTANT: This is the most common cause of VM deployment failures. Fix this before proceeding.**

The most common issue is qemu permission errors that cause VMs to fail with:
```
error: Cannot access storage file '/var/lib/one//datastores/0/X/disk.0.snap/0' (as uid:64055, gid:993): Permission denied
```

**Fix this as root user:**

```bash
# Exit to root if you're in oneadmin session
exit

# Check current qemu configuration
grep -n "^user\|^group\|^#user\|^#group" /etc/libvirt/qemu.conf

# Configure qemu to run as oneadmin user
sed -i 's/#user = "libvirt-qemu"/user = "oneadmin"/' /etc/libvirt/qemu.conf
sed -i 's/#group = "kvm"/group = "oneadmin"/' /etc/libvirt/qemu.conf

# Verify the changes
grep -n "^user\|^group" /etc/libvirt/qemu.conf
# Should show:
# 530:user = "oneadmin"
# 534:group = "oneadmin"

# Restart libvirt services
systemctl restart libvirtd
systemctl restart libvirt-guests

# Verify services are running
systemctl status libvirtd
```

**This fix is essential - without it, all VM deployments will fail with permission errors.**

## Additional Prerequisites Check

Before proceeding, verify these additional issues are resolved:

### 1. Verify OpenNebula Services
```bash
# Check OpenNebula services are running
sudo systemctl status opennebula
sudo systemctl status opennebula-sunstone
sudo systemctl status opennebula-gate  # OneGate for contextualization
```

### 2. Check Host Resources
```bash
# Switch to oneadmin user
sudo -iu oneadmin

# Verify sufficient resources
onehost list
# Ensure CPU and memory are available for new VMs
```

### 3. How to Debug VM Deployment Failures

If VMs fail to deploy, check the logs:

```bash
# As oneadmin user, check VM status
onevm list

# For a failed VM (e.g., VM ID 10), check its log
tail -20 /var/log/one/10.log

# Look for specific error patterns:
# - "Permission denied" -> Fix libvirt permissions (above)
# - "Insufficient resources" -> Check host capacity
# - "Network not found" -> Verify network names match
```

## Step 1: Network Configuration

### Create Private Network

A private network is essential for internal communication between Kubernetes cluster nodes. This network will be used by the workload cluster VMs.

#### Using Sunstone UI

1. Navigate to **Networks** → **Virtual Networks**
2. Click **Create** button
3. Enter network name: `private` (this exact name will be used in CAPI configuration)
4. Click **Next**
5. Enable **Use only private host networking**
6. Go to **Addresses** tab
7. Configure IP range:
   - **First IPv4 address**: `*************`
   - **Network size**: `100`
8. Click **Finish**

#### Using Command Line

```bash
# Create network template
cat > private-network.tmpl << EOF
NAME = "private"
VN_MAD = "dummy"
BRIDGE = "br0"
AR = [
    TYPE = "IP4",
    IP = "*************",
    SIZE = "100"
]
EOF

# Create the network
onevnet create private-network.tmpl
```

### Verify Network Creation

Confirm the network exists:
```bash
onevnet list
```

You should see both `vnet` (public) and `private` networks listed.

**Important Notes:**
- The private network name must match exactly what you'll use in the CAPI configuration
- The IP range should not conflict with your public network
- Ensure the network has sufficient IP addresses for your planned cluster size

## Step 2: Deploy CAPI Appliance

### Download from Marketplace

The CAPI appliance is available in the OpenNebula marketplace as "Service Capi".

#### Using Sunstone UI

1. Go to **Storage** → **Apps**
2. Search for "Service Capi" or "Capi"
3. Select the **Service Capi** appliance
4. Click **Download**
5. Choose destination datastore (usually `default`)
6. Wait for download completion

#### Using Command Line

```bash
# List available appliances to find the exact name
onemarketapp list | grep -i capi

# Download the appliance using the exact name
onemarketapp export 'Service Capi' Capi --datastore default
```

### Verify Template Download

After download, verify the template exists:

#### Using Sunstone UI
1. Go to **Templates** → **VMs**
2. Look for the **Capi** template

#### Using Command Line
```bash
# List VM templates
onetemplate list | grep -i capi
```

### Configure Appliance Template (Optional)

The default template should work for most cases, but you can customize if needed:

#### Using Sunstone UI

1. Go to **Templates** → **VMs**
2. Find the **Capi** template
3. Click **Update**
4. Modify configuration if needed:
   - CPU: 2 cores (minimum, 4 recommended)
   - Memory: 8 GiB (minimum, 16 GiB recommended)
   - Ensure network interface is configured

#### Using Command Line

```bash
# View template details
onetemplate show Capi

# Update template with additional resources if needed
onetemplate update Capi
```

## Step 3: Instantiate CAPI Appliance

### Launch the VM

#### Using Sunstone UI

1. Go to **Templates** → **VMs**
2. Select **Capi** template
3. Click **Instantiate**
4. Configure basic parameters:
   - **VM Name**: `rancher-capi` (or leave default)
   - **Network**: Select `vnet` (the public network)
5. Set application parameters (when prompted):
   - **ONEAPP_CAPI_RANCHER_PASSWORD**: Choose a secure password (default: `capi1234`)
   - **ONEAPP_CAPI_RANCHER_HOSTNAME**: Leave default (uses VM IP with sslip.io)
   - **ONEAPP_CAPI_CAPONE_VERSION**: Press Enter for default
   - **ONEAPP_CAPI_K3S_VERSION**: Press Enter for default
   - **ONEAPP_CAPI_RANCHER_VERSION**: Press Enter for default
   - **ONEAPP_CAPI_TURTLES_VERSION**: Press Enter for default
   - **ONEAPP_CAPI_CERT_MANAGER_VERSION**: Press Enter for default
6. Click **Instantiate**

#### Using Command Line

```bash
# Instantiate with network attachment (simplest approach)
onetemplate instantiate Capi --nic vnet

# The system will prompt for user inputs:
# - ONEAPP_CAPI_CAPONE_VERSION: Press Enter for default
# - ONEAPP_CAPI_CERT_MANAGER_VERSION: Press Enter for default
# - ONEAPP_CAPI_K3S_VERSION: Press Enter for default
# - ONEAPP_CAPI_RANCHER_HOSTNAME: Press Enter for default
# - ONEAPP_CAPI_RANCHER_PASSWORD: Enter your password or press Enter for 'capi1234'
# - ONEAPP_CAPI_RANCHER_VERSION: Press Enter for default
# - ONEAPP_CAPI_TURTLES_VERSION: Press Enter for default

# Note the VM ID returned (e.g., "VM ID: 2")
```

### Monitor Deployment Progress

The CAPI appliance initialization takes 6-8 minutes. Monitor progress:

#### Check VM Status
```bash
# List all VMs to see your CAPI appliance
onevm list

# Monitor specific VM (replace <VM_ID> with actual ID)
onevm show <VM_ID>

# Get VM IP address
onevm show <VM_ID> | grep ETH0_IP=

# Check if services are ready (after VM is running)
onevm ssh <VM_ID> "kubectl get pods -A"
```

#### Verify Services

Once running, the VM should have these services:
- K3s Kubernetes cluster (management cluster)
- Rancher management interface
- CAPI controllers (including CAPONE)
- Turtles extension
- Cert-manager

**Important Notes:**
- The complete configuration process can take 6-8 minutes with default resources
- If the Rancher UI doesn't come up, check for the `helm-install-rancher-turtles` pod issue
- You can monitor the bootstrapping process by SSH'ing into the VM and running `kubectl get pods -A`

## Step 4: Access Rancher Interface

### Get VM IP Address

```bash
# As oneadmin user, get VM IP (replace <VM_ID> with your actual VM ID)
onevm show <VM_ID> | grep ETH0_IP=

# Example output: ETH0_IP="************"
```

### Access Rancher UI (SSH Tunnel Method)

Since you're SSH'd into the OpenNebula frontend, the VM IP is not directly accessible from your local machine. Use SSH port forwarding:

#### On Your Local Machine (macOS/Linux):

1. **Create SSH tunnel** (replace with your actual SSH details):
```bash
ssh -L 8444:************:443 root@your-frontend-ip
```

2. **Add hosts entry** to handle sslip.io hostname:
```bash
echo "127.0.0.1 ************.sslip.io" | sudo tee -a /etc/hosts
```

3. **Access Rancher UI**:
   - Open browser and go to: **https://************.sslip.io:8444**
   - Accept self-signed certificate warning
   - Login with credentials:
     - **Username**: `admin`
     - **Password**: Password set during instantiation (default: `capi1234`)

#### Alternative: Direct Access (if VM has public IP)

If your VM has a public IP, you can access directly:
```
https://<VM_PUBLIC_IP>.sslip.io
```

### Initial Rancher Setup

After first login, Rancher will guide you through initial setup:
1. Accept terms and conditions
2. Set server URL (usually auto-detected as `https://<VM_IP>.sslip.io`)
3. Configure telemetry preferences (optional)
4. Complete initial setup wizard

### Verify CAPI Integration

After login, verify the CAPI integration is working:

1. In Rancher, click the **Cluster Management** icon (farmhouse icon at bottom left)
2. Navigate to **CAPI** → **Providers**
3. You should see **OpenNebula** listed as an infrastructure provider
4. The management cluster (K3s) should be visible and active

**Troubleshooting:**
- If Rancher UI doesn't load after 10 minutes, SSH into the VM and check pod status
- Look for failed `helm-install-rancher-turtles` pods and restart if necessary
- Ensure the VM has internet connectivity for downloading components

## Step 5: Deploy RKE2 Workload Cluster

### Install CAPONE Helm Chart

1. In Rancher, click the **Rancher** logo (top-left)
2. Navigate to **Apps** → **Charts**
3. Search for `capone`
4. Select **capone-rke2** chart
5. Click **Install**

### Configure Cluster Parameters

1. **Namespace**: `default`
2. **Name**: `production-k8s`
3. Click **Next**

### Customize YAML Configuration

Scroll to the bottom of the YAML configuration and modify these parameters:

```yaml
# OpenNebula connection settings
ONE_AUTH: oneadmin:your_opennebula_password
ONE_XMLRPC: http://172.16.100.1:2633/RPC2

# Network configuration
PRIVATE_NETWORK_NAME: k8s-private
PUBLIC_NETWORK_NAME: vnet

# Cluster specifications
controlPlane:
  replicas: 1
  machineTemplate:
    cpu: 2
    memory: 4096
    disk: 20480

workers:
  replicas: 2
  machineTemplate:
    cpu: 2
    memory: 4096
    disk: 20480
```

### Deploy Cluster

1. Review configuration
2. Click **Install**
3. Monitor deployment in **Apps** → **Installed Apps**

### Verify Cluster Creation

Check OpenNebula for new VMs:
```bash
onevm list
```

You should see:
- Virtual router VM
- Control plane VM(s)
- Worker node VM(s)

## Step 6: Import Cluster into Rancher

### Access Cluster Management

1. Click the **Cluster Management** icon (farmhouse icon)
2. Select **Clusters**
3. Find your cluster (e.g., `production-k8s`)
4. Click the cluster name

### Get Import Command

The cluster details page shows import commands. Copy the `curl` command (second option for self-signed certificates).

### Execute Import

1. Go to **Cluster Management** → **Clusters**
2. Click **local** cluster
3. Click three-dot menu → **Kubectl Shell**
4. In the kubectl shell, run:

```bash
# Get cluster kubeconfig
kubectl get secrets production-k8s-kubeconfig -o jsonpath="{.data.value}" | base64 -d > cluster-kubeconfig

# Import cluster (paste your specific command)
curl --insecure -sfL https://************.sslip.io/v3/import/YOUR_IMPORT_TOKEN.yaml | kubectl apply --kubeconfig cluster-kubeconfig -f -
```

### Verify Import

Return to **Cluster Management** → **Clusters**. Your workload cluster should now appear alongside the local management cluster.

## Step 7: Deploy Applications

### Install Longhorn Storage

1. Select your workload cluster
2. Go to **Apps** → **Charts**
3. Search for `longhorn`
4. Install Longhorn with default settings

### Create Persistent Volume Claim

1. Navigate to **Storage** → **PersistentVolumeClaims**
2. Click **Create**
3. Configure PVC:
   - **Name**: `web-storage`
   - **Storage Class**: `longhorn`
   - **Size**: `5Gi`
4. Click **Create**

### Deploy Sample Application

Use **Import YAML** to deploy an Nginx application:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-app
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: web-storage
          mountPath: /usr/share/nginx/html/data
      volumes:
      - name: web-storage
        persistentVolumeClaim:
          claimName: web-storage
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
```

### Verify Deployment

1. Check **Workloads** → **Deployments**
2. Verify pods are running
3. Test external access via `http://<node-ip>:30080`

## Step 8: Cluster Operations

### Scale Worker Nodes

1. Go to **Cluster Management** → **CAPI** → **Machine Deployments**
2. Find your cluster's machine deployment
3. Click three-dot menu → **Edit YAML**
4. Change `replicas: 2` to desired number
5. Save changes

### Upgrade Cluster

1. Go to **Cluster Management** → **Clusters**
2. Click three-dot menu for your cluster → **Edit Config**
3. In **Basics** section, select new Kubernetes version
4. Click **Save**
5. Monitor upgrade progress in cluster dashboard

### Monitor Cluster Health

1. Access cluster dashboard
2. Review **Cluster Tools** for monitoring options
3. Check **Events** for any issues
4. Monitor resource usage in **Cluster** → **Nodes**

## Troubleshooting

### Common Issues

**CAPI Appliance Won't Start**
- Check VM resources (CPU/Memory)
- Verify OneGate service is running
- Check VM logs: `onevm log <VM_ID>`

**Cluster Creation Fails**
- Verify OpenNebula credentials in YAML
- Check network names match exactly
- Ensure sufficient resources available

**Import Command Fails**
- Verify kubeconfig was extracted correctly
- Check network connectivity between clusters
- Confirm import token is valid

**Applications Won't Deploy**
- Check cluster has sufficient resources
- Verify Longhorn is properly installed
- Review pod logs for specific errors

### Useful Commands

```bash
# Check OpenNebula VMs
onevm list

# Monitor cluster from management cluster
kubectl get clusters -A

# Check CAPI resources
kubectl get machines -A
kubectl get machinedeployments -A

# View cluster events
kubectl get events --sort-by='.lastTimestamp'
```

## Best Practices

### Security
- Use strong passwords for Rancher
- Implement proper RBAC policies
- Regular security updates
- Network segmentation

### Resource Management
- Monitor resource usage regularly
- Set appropriate resource limits
- Plan for cluster growth
- Implement backup strategies

### Maintenance
- Regular cluster upgrades
- Monitor application health
- Backup persistent data
- Document configuration changes

## Conclusion

You've successfully deployed a complete Kubernetes management platform using OpenNebula, CAPI, and Rancher. This setup provides:

- Simplified cluster lifecycle management
- Graphical interface for operations
- Automated provisioning and scaling
- Integrated storage solutions
- Comprehensive monitoring capabilities

The combination of OpenNebula's infrastructure management with Rancher's Kubernetes expertise creates a powerful platform for container orchestration at any scale.

## Next Steps

- Explore advanced Rancher features
- Implement GitOps workflows
- Set up monitoring and alerting
- Deploy production applications
- Configure backup and disaster recovery
