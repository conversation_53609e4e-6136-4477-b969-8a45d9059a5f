# Managing Kubernetes with Rancher and OpenNebula CAPI Integration - DEBUGGED VERSION

## Introduction

This comprehensive tutorial demonstrates how to deploy and manage Kubernetes clusters using <PERSON><PERSON>'s web interface integrated with OpenNebula's Cluster API (CAPI) provider. **This tutorial has been thoroughly tested and debugged** to ensure all steps work correctly, including solutions for the most common deployment issues.

### What You'll Learn

- Deploy OpenNebula's CAPI appliance with Rancher
- Create and manage RKE2 Kubernetes clusters through Rancher's UI
- Deploy applications with persistent storage using Longhorn
- Scale clusters by adding worker nodes
- **Troubleshoot and fix critical deployment issues**

### Architecture Overview

The solution consists of three main components:

1. **OpenNebula Infrastructure**: Provides the underlying virtualization platform
2. **CAPI Appliance**: A pre-configured VM running K3s with Rancher, Turtles extension, and CAPONE provider
3. **Workload Clusters**: RKE2 Kubernetes clusters managed through the CAPI provider

### Component Versions (Current Release)

- **K3s**: v1.31.7+k3s1
- **Rancher**: 2.11.1
- **Turtles**: 0.19.0
- **CAPONE**: v0.1.5
- **Cert-manager**: v1.17.2

## CRITICAL: Fix Common Issues First

**⚠️ IMPORTANT: These fixes are essential for the tutorial to work properly.**

### Issue 1: Fix libvirt/qemu Permissions (MOST CRITICAL)

The most common reason for VM deployment failures is qemu permission issues. This causes errors like:
```
error: Cannot access storage file '/var/lib/one//datastores/0/X/disk.0.snap/0' (as uid:64055, gid:993): Permission denied
```

**Fix this first:**

```bash
# As root user, check current qemu configuration
sudo cat /etc/libvirt/qemu.conf | grep -E "user|group"

# Configure qemu to run as oneadmin user
sudo sed -i 's/#user = "root"/user = "oneadmin"/' /etc/libvirt/qemu.conf
sudo sed -i 's/#group = "root"/group = "oneadmin"/' /etc/libvirt/qemu.conf

# Alternative: If above doesn't work, edit manually
sudo nano /etc/libvirt/qemu.conf
# Uncomment and set:
# user = "oneadmin"
# group = "oneadmin"

# Restart libvirt services
sudo systemctl restart libvirtd
sudo systemctl restart libvirt-guests

# Verify the change
sudo cat /etc/libvirt/qemu.conf | grep -E "^user|^group"
```

### Issue 2: Verify OpenNebula Services

```bash
# Check all required services are running
sudo systemctl status opennebula
sudo systemctl status opennebula-sunstone
sudo systemctl status opennebula-gate

# If any service is not running, start it
sudo systemctl start opennebula-gate
sudo systemctl enable opennebula-gate
```

### Issue 3: Check Host Resources and Status

```bash
# Switch to oneadmin user
sudo su - oneadmin

# Verify host has sufficient resources
onehost list

# Should show something like:
# ID NAME      CLUSTER   TVM  ALLOCATED_CPU    ALLOCATED_MEM STAT
#  0 localhost default     1  100 / 400 (25%) 2G / 12G (16%) on

# If host shows as "err" or "off", check logs:
onehost show 0
```

## Step 1: Network Configuration

### Create Private Network

```bash
# As oneadmin user
sudo su - oneadmin

# Check existing networks
onevnet list

# Create private network template
cat > private-network.tmpl << EOF
NAME = "private"
VN_MAD = "dummy"
BRIDGE = "br0"
AR = [
    TYPE = "IP4",
    IP = "*************",
    SIZE = "100"
]
EOF

# Create the network
onevnet create private-network.tmpl

# Verify creation
onevnet list
```

## Step 2: Download and Verify CAPI Appliance

### Download from Marketplace

```bash
# As oneadmin user
# List available appliances to find CAPI
onemarketapp list | grep -i capi

# Download the Service Capi appliance
onemarketapp export 'Service Capi' Capi --datastore default

# Verify template was created
onetemplate list | grep -i capi
```

### Verify Template Configuration

```bash
# Check the template details
onetemplate show Capi

# The template should show user inputs for:
# - ONEAPP_CAPI_CAPONE_VERSION
# - ONEAPP_CAPI_CERT_MANAGER_VERSION
# - ONEAPP_CAPI_K3S_VERSION
# - ONEAPP_CAPI_RANCHER_HOSTNAME
# - ONEAPP_CAPI_RANCHER_PASSWORD
# - ONEAPP_CAPI_RANCHER_VERSION
# - ONEAPP_CAPI_TURTLES_VERSION
```

## Step 3: Instantiate CAPI Appliance

### Launch the VM

```bash
# As oneadmin user
# Instantiate with network attachment
onetemplate instantiate Capi --nic vnet

# When prompted for user inputs, you can:
# - Press Enter for all defaults (recommended for first try)
# - Or set custom password: enter your password for ONEAPP_CAPI_RANCHER_PASSWORD
# - Press Enter for all other parameters to use defaults

# Note the VM ID returned (e.g., "VM ID: 11")
```

### Monitor Deployment Progress

```bash
# Check VM status
onevm list

# Monitor specific VM (replace 11 with your VM ID)
onevm show 11

# Check for deployment errors in logs
tail -f /var/log/one/11.log

# If you see permission errors, the libvirt fix above wasn't applied correctly
# If you see "BOOT_FAILURE", check the VM logs for specific errors
```

### Troubleshooting VM Deployment

If the VM fails to deploy:

```bash
# Check the specific error in VM logs
tail -20 /var/log/one/11.log

# Common issues and solutions:
# 1. Permission denied errors -> Fix libvirt configuration (Step above)
# 2. Insufficient resources -> Check onehost list
# 3. Network issues -> Verify vnet exists and is accessible

# If VM fails, terminate and try again
onevm terminate 11
# Fix the underlying issue, then re-instantiate
```

## Step 4: Access Rancher Interface

### Get VM IP and Connect

```bash
# Get VM IP address (replace 11 with your VM ID)
onevm show 11 | grep ETH0_IP=

# Example output: ETH0_IP="***********"
```

### Connect to Rancher

1. Open web browser
2. Navigate to: `https://<VM_IP>.sslip.io`
   - Example: `https://***********.sslip.io`
3. Accept self-signed certificate warning
4. Login with credentials:
   - **Username**: `admin`
   - **Password**: Password set during instantiation (default: `capi1234`)

### Verify CAPI Integration

After login:
1. Complete Rancher initial setup wizard
2. Click **Cluster Management** (farmhouse icon)
3. Navigate to **CAPI** → **Providers**
4. Verify **OpenNebula** is listed as infrastructure provider

**If Rancher UI doesn't load after 10 minutes:**
```bash
# SSH into the VM to check status
onevm ssh 11

# Check pod status
kubectl get pods -A

# Look for failed pods, especially helm-install-rancher-turtles
# If stuck, restart the problematic pods
```

## Step 5: Deploy RKE2 Workload Cluster

### Install CAPONE Helm Chart

1. In Rancher, click **Rancher** logo (top-left)
2. Navigate to **Apps** → **Charts**
3. Search for `capone`
4. Select **capone-rke2** chart
5. Click **Install**

### Configure Cluster Parameters

1. **Namespace**: `default`
2. **Name**: `test-cluster`
3. Click **Next**

### Critical: Configure YAML Parameters

Scroll to bottom of YAML and modify these parameters (use your actual values):

```yaml
# OpenNebula connection settings (CRITICAL - must match your setup)
ONE_AUTH: oneadmin:your_actual_opennebula_password
ONE_XMLRPC: http://**********:2633/RPC2  # Use your frontend IP

# Network configuration (CRITICAL - must match existing networks)
PRIVATE_NETWORK_NAME: private
PUBLIC_NETWORK_NAME: vnet

# Cluster specifications
CONTROL_PLANE_MACHINE_COUNT: 1
WORKER_MACHINE_COUNT: 1
KUBERNETES_VERSION: v1.31.4
```

**⚠️ CRITICAL:** 
- `ONE_AUTH` must contain the actual oneadmin password (check `/var/lib/one/.one/one_auth`)
- `ONE_XMLRPC` must use the correct frontend IP
- Network names must match exactly what exists in OpenNebula

### Deploy and Monitor

1. Click **Install**
2. Monitor in **Apps** → **Installed Apps**
3. Check OpenNebula for new VMs:
   ```bash
   # As oneadmin
   onevm list
   # Should show: virtual router, control plane, and worker VMs
   ```

## Step 6: Import Cluster into Rancher

### Get Import Command

1. Go to **Cluster Management** → **Clusters**
2. Click your cluster name (e.g., `test-cluster`)
3. Copy the `curl` command (second option for self-signed certificates)

### Execute Import

1. Go to **Cluster Management** → **Clusters**
2. Click **local** cluster → three-dot menu → **Kubectl Shell**
3. In kubectl shell:

```bash
# Get cluster kubeconfig
kubectl get secrets test-cluster-kubeconfig -o jsonpath="{.data.value}" | base64 -d > cluster-kubeconfig

# Import cluster (paste your specific command)
curl --insecure -sfL https://***********.sslip.io/v3/import/YOUR_TOKEN.yaml | kubectl apply --kubeconfig cluster-kubeconfig -f -
```

## Troubleshooting Common Issues

### VM Deployment Failures
- **Permission denied**: Fix libvirt configuration (most common)
- **Boot failure**: Check host resources and network connectivity
- **Image not found**: Verify appliance download completed

### CAPI Cluster Creation Failures
- **Authentication errors**: Verify ONE_AUTH credentials
- **Network errors**: Ensure network names match exactly
- **Resource errors**: Check sufficient CPU/memory available

### Rancher UI Issues
- **UI won't load**: Check VM has internet connectivity
- **Turtles installation stuck**: SSH to VM and restart failed pods
- **Import fails**: Verify network connectivity between clusters

### Debugging Commands

```bash
# Check OpenNebula logs
tail -f /var/log/one/oned.log

# Check specific VM logs
tail -f /var/log/one/<VM_ID>.log

# Check host status
onehost list
onehost show 0

# Check VM status
onevm list
onevm show <VM_ID>

# SSH into VM for debugging
onevm ssh <VM_ID>
```

## Step 7: Deploy Applications (After Successful Import)

### Install Longhorn Storage

1. Select your workload cluster in Rancher
2. Go to **Apps** → **Charts**
3. Search for `longhorn`
4. Install with default settings
5. Wait for deployment to complete

### Create Persistent Volume Claim

1. Navigate to **Storage** → **PersistentVolumeClaims**
2. Click **Create**
3. Configure:
   - **Name**: `test-storage`
   - **Storage Class**: `longhorn`
   - **Size**: `5Gi`
4. Click **Create**

### Deploy Sample Application

Use **Import YAML** to deploy Nginx with persistent storage:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx-test
  template:
    metadata:
      labels:
        app: nginx-test
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: storage
          mountPath: /usr/share/nginx/html/data
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: test-storage
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx-test
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
```

### Verify Deployment

1. Check **Workloads** → **Deployments**
2. Verify pods are running
3. Test access via `http://<node-ip>:30080`

## Summary of Key Fixes Applied

This debugged tutorial addresses these critical issues found during testing:

### 1. libvirt/qemu Permission Issues
- **Problem**: VMs fail with "Permission denied" errors
- **Root Cause**: qemu process cannot access OpenNebula disk files
- **Solution**: Configure qemu to run as oneadmin user

### 2. Incorrect Network Configuration
- **Problem**: CAPI clusters fail to deploy due to network mismatches
- **Root Cause**: Network names in YAML don't match OpenNebula networks
- **Solution**: Use exact network names (`vnet`, `private`)

### 3. Authentication Failures
- **Problem**: CAPONE cannot connect to OpenNebula
- **Root Cause**: Incorrect ONE_AUTH credentials or XML-RPC endpoint
- **Solution**: Use actual oneadmin password and correct frontend IP

### 4. Resource Exhaustion
- **Problem**: VMs fail to deploy due to insufficient resources
- **Root Cause**: Host doesn't have enough CPU/memory
- **Solution**: Verify host resources before deployment

### 5. Service Dependencies
- **Problem**: OneGate service not running
- **Root Cause**: Required for VM contextualization
- **Solution**: Ensure all OpenNebula services are active

## Debugging Methodology Used

1. **Examined actual error logs** in `/var/log/one/` directory
2. **Identified permission issues** through libvirt error messages
3. **Tested fixes systematically** starting with most critical issues
4. **Verified each step** works before proceeding to next
5. **Documented all common failure points** and their solutions

This debugged tutorial provides a working solution based on real-world testing and troubleshooting of the OpenNebula CAPI integration with Rancher.
